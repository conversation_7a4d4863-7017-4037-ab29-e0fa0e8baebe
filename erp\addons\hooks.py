"""
Addon lifecycle hooks system
Provides pre/post hooks for install, uninstall, and upgrade operations
"""
from typing import Dict, List, Callable, Any, Optional, Union
from enum import Enum
import asyncio
import inspect
from functools import wraps
import logging

logger = logging.getLogger(__name__)


class HookType(Enum):
    """Types of addon lifecycle hooks"""
    PRE_INSTALL = "pre_install"
    POST_INSTALL = "post_install"
    PRE_UNINSTALL = "pre_uninstall"
    POST_UNINSTALL = "post_uninstall"
    PRE_UPGRADE = "pre_upgrade"
    POST_UPGRADE = "post_upgrade"


class HookContext:
    """Context passed to lifecycle hooks"""
    
    def __init__(
        self, 
        addon_name: str, 
        hook_type: HookType, 
        env: 'Environment' = None,
        **kwargs
    ):
        self.addon_name = addon_name
        self.hook_type = hook_type
        self.env = env
        self.data = kwargs
    
    def __repr__(self):
        return f"HookContext(addon={self.addon_name}, type={self.hook_type.value})"


class AddonHook:
    """Represents a single addon hook"""
    
    def __init__(
        self, 
        func: Callable, 
        hook_type: HookType, 
        addon_name: str,
        priority: int = 50
    ):
        self.func = func
        self.hook_type = hook_type
        self.addon_name = addon_name
        self.priority = priority
        self.is_async = asyncio.iscoroutinefunction(func)
    
    async def execute(self, context: HookContext) -> Any:
        """Execute the hook function"""
        try:
            if self.is_async:
                return await self.func(context)
            else:
                return self.func(context)
        except Exception as e:
            logger.error(f"Error executing {self.hook_type.value} hook for {self.addon_name}: {e}")
            raise
    
    def __repr__(self):
        return f"AddonHook(addon={self.addon_name}, type={self.hook_type.value}, priority={self.priority})"


class HookRegistry:
    """Registry for managing addon lifecycle hooks"""
    
    def __init__(self):
        self._hooks: Dict[HookType, List[AddonHook]] = {
            hook_type: [] for hook_type in HookType
        }
    
    def register_hook(
        self, 
        func: Callable, 
        hook_type: HookType, 
        addon_name: str,
        priority: int = 50
    ):
        """Register a lifecycle hook"""
        hook = AddonHook(func, hook_type, addon_name, priority)
        self._hooks[hook_type].append(hook)
        
        # Sort hooks by priority (lower number = higher priority)
        self._hooks[hook_type].sort(key=lambda h: h.priority)
        
        logger.debug(f"Registered {hook_type.value} hook for addon {addon_name}")
    
    def unregister_addon_hooks(self, addon_name: str):
        """Remove all hooks for a specific addon"""
        for hook_type in HookType:
            self._hooks[hook_type] = [
                hook for hook in self._hooks[hook_type] 
                if hook.addon_name != addon_name
            ]
        logger.debug(f"Unregistered all hooks for addon {addon_name}")
    
    def get_hooks(self, hook_type: HookType) -> List[AddonHook]:
        """Get all hooks for a specific type"""
        return self._hooks[hook_type].copy()
    
    async def execute_hooks(
        self, 
        hook_type: HookType, 
        addon_name: str, 
        env: 'Environment' = None,
        **kwargs
    ) -> List[Any]:
        """Execute all hooks of a specific type"""
        hooks = self.get_hooks(hook_type)
        context = HookContext(addon_name, hook_type, env, **kwargs)
        results = []
        
        logger.info(f"Executing {len(hooks)} {hook_type.value} hooks for addon {addon_name}")
        
        for hook in hooks:
            try:
                result = await hook.execute(context)
                results.append(result)
            except Exception as e:
                logger.error(f"Hook execution failed: {e}")
                # Continue executing other hooks even if one fails
                results.append(None)
        
        return results


# Global hook registry
_hook_registry = HookRegistry()


def get_hook_registry() -> HookRegistry:
    """Get the global hook registry"""
    return _hook_registry


# Decorator functions for registering hooks
def pre_install_hook(addon_name: str = None, priority: int = 50):
    """Decorator for pre-install hooks"""
    def decorator(func: Callable):
        nonlocal addon_name
        if addon_name is None:
            # Try to infer addon name from module
            module = inspect.getmodule(func)
            if module and hasattr(module, '__name__'):
                parts = module.__name__.split('.')
                if len(parts) >= 3 and parts[0] == 'erp' and parts[1] == 'addons':
                    addon_name = parts[2]
        
        if addon_name is None:
            raise ValueError("Could not determine addon name. Please specify explicitly.")
        
        _hook_registry.register_hook(func, HookType.PRE_INSTALL, addon_name, priority)
        return func
    return decorator


def post_install_hook(addon_name: str = None, priority: int = 50):
    """Decorator for post-install hooks"""
    def decorator(func: Callable):
        nonlocal addon_name
        if addon_name is None:
            module = inspect.getmodule(func)
            if module and hasattr(module, '__name__'):
                parts = module.__name__.split('.')
                if len(parts) >= 3 and parts[0] == 'erp' and parts[1] == 'addons':
                    addon_name = parts[2]
        
        if addon_name is None:
            raise ValueError("Could not determine addon name. Please specify explicitly.")
        
        _hook_registry.register_hook(func, HookType.POST_INSTALL, addon_name, priority)
        return func
    return decorator


def pre_uninstall_hook(addon_name: str = None, priority: int = 50):
    """Decorator for pre-uninstall hooks"""
    def decorator(func: Callable):
        nonlocal addon_name
        if addon_name is None:
            module = inspect.getmodule(func)
            if module and hasattr(module, '__name__'):
                parts = module.__name__.split('.')
                if len(parts) >= 3 and parts[0] == 'erp' and parts[1] == 'addons':
                    addon_name = parts[2]
        
        if addon_name is None:
            raise ValueError("Could not determine addon name. Please specify explicitly.")
        
        _hook_registry.register_hook(func, HookType.PRE_UNINSTALL, addon_name, priority)
        return func
    return decorator


def post_uninstall_hook(addon_name: str = None, priority: int = 50):
    """Decorator for post-uninstall hooks"""
    def decorator(func: Callable):
        nonlocal addon_name
        if addon_name is None:
            module = inspect.getmodule(func)
            if module and hasattr(module, '__name__'):
                parts = module.__name__.split('.')
                if len(parts) >= 3 and parts[0] == 'erp' and parts[1] == 'addons':
                    addon_name = parts[2]
        
        if addon_name is None:
            raise ValueError("Could not determine addon name. Please specify explicitly.")
        
        _hook_registry.register_hook(func, HookType.POST_UNINSTALL, addon_name, priority)
        return func
    return decorator


def pre_upgrade_hook(addon_name: str = None, priority: int = 50):
    """Decorator for pre-upgrade hooks"""
    def decorator(func: Callable):
        nonlocal addon_name
        if addon_name is None:
            module = inspect.getmodule(func)
            if module and hasattr(module, '__name__'):
                parts = module.__name__.split('.')
                if len(parts) >= 3 and parts[0] == 'erp' and parts[1] == 'addons':
                    addon_name = parts[2]
        
        if addon_name is None:
            raise ValueError("Could not determine addon name. Please specify explicitly.")
        
        _hook_registry.register_hook(func, HookType.PRE_UPGRADE, addon_name, priority)
        return func
    return decorator


def post_upgrade_hook(addon_name: str = None, priority: int = 50):
    """Decorator for post-upgrade hooks"""
    def decorator(func: Callable):
        nonlocal addon_name
        if addon_name is None:
            module = inspect.getmodule(func)
            if module and hasattr(module, '__name__'):
                parts = module.__name__.split('.')
                if len(parts) >= 3 and parts[0] == 'erp' and parts[1] == 'addons':
                    addon_name = parts[2]
        
        if addon_name is None:
            raise ValueError("Could not determine addon name. Please specify explicitly.")
        
        _hook_registry.register_hook(func, HookType.POST_UPGRADE, addon_name, priority)
        return func
    return decorator
